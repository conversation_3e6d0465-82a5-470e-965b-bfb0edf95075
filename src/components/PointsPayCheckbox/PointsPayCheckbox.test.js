import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import PointsPayCheckbox from './PointsPayCheckbox';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import { getPointsBalance } from 'store/user/userSelectors';
import { useIsAuthenticated } from 'lib/oauth';
import { setIsPointsPay } from 'store/ui/uiActions';
import { MIN_POINTS_AMOUNT } from 'config';
import { updateQuery } from 'store/propertyAvailability/propertyAvailabilityActions';
import { PAYMENT_METHODS } from 'lib/enums/payment';

jest.mock('store/ui/uiSelectors');
jest.mock('store/user/userSelectors');
jest.mock('lib/oauth');

let mockDispatch;

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: () => mockDispatch,
}));

// Helper functions to reduce duplication
const getCheckbox = () => screen.getByRole('checkbox');

const expectCheckboxToBeCheckedAndEnabled = () => {
  const checkbox = getCheckbox();
  expect(checkbox).toBeChecked();
  expect(checkbox).not.toBeDisabled();
  expect(checkbox).toHaveAttribute('autocomplete', 'off');
  return checkbox;
};

const expectCheckboxToBeEnabled = () => {
  const checkbox = getCheckbox();
  expect(checkbox).not.toBeDisabled();
  return checkbox;
};

const expectNoDispatchOfSetIsPointsPayFalse = (store) => {
  expect(store.dispatch).not.toHaveBeenCalledWith(setIsPointsPay(false));
};

const setupUserClick = async () => {
  const user = userEvent.setup();
  const { store } = render();
  const checkbox = getCheckbox();
  return { user, store, checkbox };
};

const render = (props = {}, initialState = {}) => {
  mockDispatch = jest.fn();

  return {
    ...renderWithProviders(<PointsPayCheckbox {...props} />, initialState),
    store: { dispatch: mockDispatch },
  };
};

const isPointsPay = true;

beforeEach(() => {
  getIsPointsPay.mockReturnValue(isPointsPay);
  useIsAuthenticated.mockReturnValue(false);
});

describe('when not logged in', () => {
  it('renders with expected props', () => {
    render();
    expectCheckboxToBeCheckedAndEnabled();
  });

  it('renders with a custom font size', () => {
    render({ size: 'xl' });
    expectCheckboxToBeCheckedAndEnabled();

    const textElements = screen.getAllByText(/Use Points \+ Pay|Minimum.*pts required/);
    expect(textElements).toHaveLength(2);
  });

  it('does not dispatch setIsPointsPay(false)', () => {
    const { store } = render();
    expectNoDispatchOfSetIsPointsPayFalse(store);
  });

  it('hides points description when showPointsDescription is false', () => {
    render({ showPointsDescription: false });
    const pointsDescription = screen.queryByText(/Minimum.*pts required/);
    expect(pointsDescription).not.toBeInTheDocument();
  });
});

describe('when logged in', () => {
  beforeEach(() => {
    useIsAuthenticated.mockReturnValue(true);
  });

  describe('with a points balance under the minimum', () => {
    beforeEach(() => {
      getPointsBalance.mockReturnValue(MIN_POINTS_AMOUNT.minus(1));
    });

    it('renders disabled', () => {
      render();
      const checkbox = getCheckbox();
      expect(checkbox).toBeDisabled();
    });

    it('dispatches setIsPointsPay(false)', async () => {
      const { store } = render();
      await waitFor(() => {
        expect(store.dispatch).toHaveBeenCalledWith(setIsPointsPay(false));
      });
    });
  });

  // Combine the "at minimum" and "above minimum" scenarios since they have identical behavior
  describe('with sufficient points balance (at or above minimum)', () => {
    const testCases = [
      { description: 'at the minimum', balance: MIN_POINTS_AMOUNT },
      { description: 'above the minimum', balance: MIN_POINTS_AMOUNT.plus(1000) },
    ];

    testCases.forEach(({ description, balance }) => {
      describe(description, () => {
        beforeEach(() => {
          getPointsBalance.mockReturnValue(balance);
        });

        it('renders enabled', () => {
          render();
          expectCheckboxToBeEnabled();
        });

        it('does not dispatch setIsPointsPay(false)', () => {
          const { store } = render();
          expectNoDispatchOfSetIsPointsPayFalse(store);
        });
      });
    });
  });
});

describe('toggling the state', () => {
  it('dispatches the setIsPointsPay action', async () => {
    const { user, store, checkbox } = await setupUserClick();

    await user.click(checkbox);
    expect(store.dispatch).toHaveBeenCalledWith(setIsPointsPay(!isPointsPay));
  });

  it('dispatches updateQuery when checking the checkbox', async () => {
    getIsPointsPay.mockReturnValue(false);
    const { user, store, checkbox } = await setupUserClick();

    await user.click(checkbox);
    expect(store.dispatch).toHaveBeenCalledWith(updateQuery({ payWith: PAYMENT_METHODS.POINTS }));
  });

  it('does not dispatch updateQuery when unchecking the checkbox', async () => {
    getIsPointsPay.mockReturnValue(true);
    const { user, store, checkbox } = await setupUserClick();

    await user.click(checkbox);
    expect(store.dispatch).not.toHaveBeenCalledWith(updateQuery({ payWith: PAYMENT_METHODS.POINTS }));
    expect(store.dispatch).toHaveBeenCalledWith(setIsPointsPay(false));
  });

  it('emits a gtm event when clicked', async () => {
    const { user, store, checkbox } = await setupUserClick();

    await user.click(checkbox);
    // Just verify that dispatch was called more than once (for both the action and the GTM event)
    expect(store.dispatch).toHaveBeenCalledTimes(2);
  });
});
